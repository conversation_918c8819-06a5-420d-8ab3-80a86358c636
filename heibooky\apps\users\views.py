import logging

from django.contrib.auth import authenticate, logout
from django.contrib.gis.geoip2 import GeoIP2
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone
from rest_framework import generics, status, views
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.parsers import Form<PERSON>ars<PERSON>, MultiPartParser
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework_simplejwt.tokens import (
    BlacklistedToken,
    OutstandingToken,
    RefreshToken,
)
from services.email import AccountEmailService, VerificationService
from user_agents import parse

from .models import User, UserProfile
from .serializers import (
    ChangePasswordSerializer,
    LoginSerializer,
    PasswordResetSerializer,
    SetNewPasswordSerializer,
    SetPasswordSerializer,
    TeamUserSerializer,
    UserProfileSerializer,
    UserSerializer,
    VerifyEmailSerializer,
)
from .tasks import login_location_task
from .utils import format_error_response, get_client_ip

logger = logging.getLogger(__name__)


class EmailVerificationMixin:
    """Mixin to handle common email verification operations"""

    def _get_verification_services(self):
        """Get instances of email and verification services"""
        return AccountEmailService(), VerificationService()

    def send_verification_code(self, email: str) -> tuple[bool, str]:
        """Send verification code and return status and message"""
        email_service, verification_service = self._get_verification_services()
        try:
            code = verification_service.generate_code()
            email_service.send_verification_email(email, code)
            verification_service.store_code(email, code)
            return True, "Verification code has been sent to your email."
        except Exception as e:
            return False, str(e)

    def verify_code(self, email: str, code: str) -> bool:
        """Verify the provided code"""
        _, verification_service = self._get_verification_services()
        return verification_service.verify_code(email, code)


class SignupView(EmailVerificationMixin, generics.CreateAPIView):
    serializer_class = UserSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)

            # Validate but handle errors manually instead of raising exceptions
            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user = serializer.save()

            success, message = self.send_verification_code(user.email)
            if not success:
                user.delete()  # Rollback user creation if email sending fails
                return Response(
                    {"errors": {"email": [message]}},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            return Response(
                {
                    "user": UserSerializer(
                        user, context=self.get_serializer_context()
                    ).data,
                    "detail": message,
                },
                status=status.HTTP_201_CREATED,
            )
        except ValidationError as e:
            # Handle DRF ValidationError
            return Response(
                format_error_response(e), status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Handle other exceptions
            logger.error(f"Signup error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class VerifyEmailView(EmailVerificationMixin, views.APIView):
    serializer_class = VerifyEmailSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(data=request.data)

            # Validate but handle errors manually
            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            email = serializer.validated_data["email"]
            verification_code = serializer.validated_data["verification_code"]

            if self.verify_code(email, verification_code):
                user = User.objects.get(email=email)
                user.is_verified = True
                user.save()
                return Response(
                    {"detail": "Email successfully verified."},
                    status=status.HTTP_200_OK,
                )

            return Response(
                {"errors": {"verification_code": ["Invalid verification code."]}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except ObjectDoesNotExist:
            return Response(
                {"errors": {"email": ["User not found."]}},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValidationError as e:
            return Response(
                format_error_response(e), status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Email verification error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )


class SetPasswordView(generics.GenericAPIView):
    serializer_class = SetPasswordSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            email = serializer.validated_data["email"]
            user = User.objects.get(email=email)

            if user.has_set_password:
                return Response(
                    {"detail": "Password has already been set for this user."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not user.is_verified:
                return Response(
                    {"detail": "Email not verified."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user.set_password(serializer.validated_data["password"])
            user.has_set_password = True
            user.save()

            # Send welcome email after password is set
            try:
                email_service = AccountEmailService()
                email_service.send_welcome_email(user)
            except Exception as e:
                logger.warning(f"Failed to send welcome email: {str(e)}")

            return Response(
                {"detail": "Password set successfully."}, status=status.HTTP_200_OK
            )

        except ObjectDoesNotExist:
            return Response(
                {"detail": "User not found."}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetRequestView(EmailVerificationMixin, generics.GenericAPIView):
    serializer_class = PasswordResetSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            email = serializer.validated_data["email"]
            user = User.objects.filter(email=email).first()

            if not user:
                return Response(
                    {"detail": "User with this email does not exist."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            email_service = AccountEmailService()
            verification_service = VerificationService()
            code = verification_service.generate_code()

            try:
                email_service.send_password_reset_email(email, code)
                verification_service.store_code(email, code)
                return Response(
                    {"detail": "Password reset instructions have been sent."},
                    status=status.HTTP_200_OK,
                )
            except Exception as e:
                logger.error(f"Failed to send password reset email: {str(e)}")
                return Response(
                    {"detail": "Failed to send reset instructions."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetConfirmView(EmailVerificationMixin, generics.GenericAPIView):
    serializer_class = SetNewPasswordSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            email = serializer.validated_data["email"]
            verification_code = serializer.validated_data["verification_code"]

            if self.verify_code(email, verification_code):
                user = User.objects.get(email=email)
                user.set_password(serializer.validated_data["password"])
                user.save()
                return Response(
                    {"detail": "Password has been reset successfully."},
                    status=status.HTTP_200_OK,
                )

            return Response(
                {"detail": "Invalid verification code."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except ObjectDoesNotExist:
            return Response(
                {"detail": "User not found."}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LoginView(ObtainAuthToken):
    serializer_class = LoginSerializer

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Get client information before authentication
            ip_address = get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")

            # Authenticate user
            user = authenticate(
                request,
                username=serializer.validated_data["email"],
                password=serializer.validated_data["password"],
            )

            if not user or not user.is_verified:
                logger.warning(f"Failed login attempt from {ip_address}")
                return Response(
                    {"detail": "Invalid credentials or email verification incomplete"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            # Start async location processing
            login_location_task.delay(
                user_id=str(user.id),
                ip_address=ip_address,
                user_agent=user_agent,
                login_time=timezone.now().isoformat(),
            )

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "user": {
                        "id": str(user.id),
                        "email": user.email,
                        "name": user.name,
                        "is_verified": user.is_verified,
                        "is_admin": user.is_admin,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Login error: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Authentication failed. Please try again."},
                status=status.HTTP_400_BAD_REQUEST,
            )


class SupportLoginView(LoginView):
    """Special login endpoint for staff and admin users only."""

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Get client information before authentication
            ip_address = get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")

            # Authenticate user
            user = authenticate(
                request,
                username=serializer.validated_data["email"],
                password=serializer.validated_data["password"],
            )

            if not user or not user.is_verified:
                logger.warning(f"Failed staff login attempt from {ip_address}")
                return Response(
                    {"detail": "Invalid credentials or email verification incomplete"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            # Staff-specific check
            if not (user.is_staff or user.is_admin):
                logger.warning(
                    f"Non-staff user attempted to access staff login: {user.email}"
                )
                return Response(
                    {"detail": "Access denied. Staff credentials required."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Start async location processing
            login_location_task.delay(
                user_id=str(user.id),
                ip_address=ip_address,
                user_agent=user_agent,
                login_time=timezone.now().isoformat(),
            )

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "user": {
                        "id": str(user.id),
                        "email": user.email,
                        "name": user.name,
                        "is_verified": user.is_verified,
                        "is_admin": user.is_admin,
                        "is_staff": user.is_staff,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Staff login error: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Staff authentication failed. Please try again."},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UserProfileView(generics.RetrieveUpdateAPIView):
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get_object(self):
        """
        Returns the UserProfile instance for the authenticated user.
        Creates it if it doesn't exist.
        """
        profile = UserProfile.objects.get(user=self.request.user)
        return profile

    def perform_update(self, serializer):
        try:
            user = self.request.user
            phone = self.request.data.get("phone")
            name = self.request.data.get("name")

            # Update user fields if provided
            if phone or name:
                user.phone = phone if phone else user.phone
                user.name = name if name else user.name
                user.save()

            # Update profile image if provided
            if "image" in self.request.data:
                serializer.save(user=user, image=self.request.data["image"])
            else:
                serializer.save(user=user)

        except Exception as e:
            raise ValidationError({"detail": str(e)})


class LogoutView(views.APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            Token.objects.filter(user=request.user).delete()  # Delete the auth token
            # Manually log out the user
            logout(request)

            # Revoke the refresh token
            refresh_token = request.data.get("refresh")
            if not refresh_token:
                return Response(
                    {"detail": "Refresh token not provided."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                token = RefreshToken(refresh_token)
                token.blacklist()  # Blacklist the refresh token and, by extension, the access token
            except Exception as e:
                return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

            return Response(
                {"detail": "Successfully logged out and tokens revoked."},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AccountDeleteView(views.APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request):
        try:
            request.user.delete()
            return Response(
                {"detail": "Account deleted successfully."}, status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ChangePasswordSerializer

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Verify current password
            user = request.user
            if not user.check_password(serializer.validated_data["current_password"]):
                return Response(
                    {"detail": "Current password is incorrect."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Change password
            user.set_password(serializer.validated_data["new_password"])
            user.save()

            # Logout from all sessions if requested
            if serializer.validated_data.get("logout_all_sessions"):
                tokens = OutstandingToken.objects.filter(user=user)
                for token in tokens:
                    BlacklistedToken.objects.get_or_create(token=token)

            # Get device and location info for security notification
            user_agent_string = request.META.get("HTTP_USER_AGENT", "")
            user_agent = parse(user_agent_string)
            device = f"{user_agent.browser.family} on {user_agent.os.family}"

            # Get location info using GeoIP2
            try:
                g = GeoIP2()
                ip = request.META.get("REMOTE_ADDR")
                location_info = g.city(ip)
                location = f"{location_info['city']}, {location_info['country_name']}"
            except Exception:
                location = "Unknown location"

            # Send security notification email
            try:
                email_service = AccountEmailService()
                email_service.send_password_changed_email(
                    user.email,
                    {
                        "change_time": timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "location": location,
                        "device": device,
                    },
                )
            except Exception as e:
                # Log the error but don't fail the request
                logger.error(f"Failed to send password change email: {str(e)}")

            return Response(
                {"detail": "Password changed successfully."}, status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TeamSignupView(generics.CreateAPIView):
    serializer_class = TeamUserSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)

            # Validate but handle errors manually
            if not serializer.is_valid():
                return Response(
                    format_error_response(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if the invite exists and is valid
            from apps.stay.models import TeamInvite

            team_invite = TeamInvite.objects.filter(
                id=serializer.validated_data["invite_id"],
                email=serializer.validated_data["email"],
                is_registered=False,
                accepted=False,
            ).first()

            if not team_invite:
                return Response(
                    {"errors": {"invite_id": ["Invalid or expired team invitation."]}},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Create the user
            user = serializer.save()

            from apps.stay.utils import approve_invite

            if approve_invite(team_invite, user):
                try:
                    email_service = AccountEmailService()
                    email_service.send_welcome_email(user)
                except Exception as e:
                    logger.warning(f"Failed to send welcome email: {str(e)}")

                return Response(
                    {
                        "user": UserSerializer(
                            user, context=self.get_serializer_context()
                        ).data,
                        "detail": "Team member registered successfully.",
                    },
                    status=status.HTTP_201_CREATED,
                )

        except ValidationError as e:
            # Handle DRF ValidationError
            return Response(
                format_error_response(e), status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Handle other exceptions
            logger.error(f"Team signup error: {str(e)}", exc_info=True)
            return Response(
                {"errors": {"general": [f"An unexpected error occurred: {str(e)}"]}},
                status=status.HTTP_400_BAD_REQUEST,
            )
